<?php

namespace App\Models;

use App\Traits\HasUuid;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Conversation extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'type',
        'sender_id',
        'receiver_id',
        'title',
        'last_message_at',
        'last_message_id',
        'is_active'
    ];

    function messages(){
        return $this->hasMany(Message::class);
    }

    function sender(){
        return $this->hasOne(User::class , "id", "sender_id")->select(['id', 'ids', 'name', 'avatar'])->with('roles');
    }
    function receiver(){
        return $this->hasOne(User::class , "id", "receiver_id")->select(['id', 'ids', 'name', 'avatar'])->with('roles');
    }
     public function lastMessage(): HasOne
    {
        return $this->hasOne(Message::class, 'id', 'last_message_id');
    }
    public function getReceiverUserAttribute()
    {
        return $this->sender_id == auth()->id() ? $this->receiver : $this->sender;
    }
}
