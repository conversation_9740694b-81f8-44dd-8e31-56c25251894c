@extends('website.layout.master')

@push('css')
    <link rel="stylesheet" href="{{ asset('website/messenger/style.css') }}" />

    <style>
        .chat_inner_search {
            /* position: absolute; */
            display: none;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            background: #fff;
            /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15); */
            /* padding: 10px; */
            width: 300px; /* Adjust as needed */
            flex: 1
        }
        .chat_inner_search.show {
            opacity: 1;
            transform: translateX(0);
        }

        /* Role badge styles */
        .user-role-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 5px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .user-role-badge.role-provider {
            background-color: #28a745;
            color: white;
        }

        .user-role-badge.role-traveller {
            background-color: #007bff;
            color: white;
        }

        .user-role-badge.role-admin {
            background-color: #dc3545;
            color: white;
        }

        .user-role-badge.role-user {
            background-color: #6c757d;
            color: white;
        }

        /* Conversation thread role-based styling */
        /* .single_chat_thread.role-provider {
            border-left: 3px solid #28a745;
        } */

        /* .single_chat_thread.role-traveller {
            border-left: 3px solid #007bff;
        } */

        /* .single_chat_thread.role-admin {
            border-left: 3px solid #dc3545;
        } */

        /* .single_chat_thread.role-user {
            border-left: 3px solid #6c757d;
        } */

    </style>

@endpush


@section('content')
    <section class="messenger_main_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="inner_section_messenger_main_col">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="messenger_wrapper">
                                    <div class="row">
                                        <div class="col-md-3 chat_sidebar_col">
                                            <div class="inner_section_messenger_sidebar">
                                                <div class="sidebar_chats_toggle_buttons_wrapper">
                                                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">

                                                        <li class="nav-item dropdown d-flex align-items-center" role="presentation">
                                                            <button class="nav-link active" id="pills-conversations-tab"
                                                                data-bs-toggle="pill"
                                                                data-bs-target="#pills-conversations"
                                                                type="button"
                                                                role="tab"
                                                                aria-controls="pills-conversations"
                                                                aria-selected="true">
                                                                <span><img src="{{ asset('website') }}/images/chat_all_icon.svg" alt=""></span><span>All</span>
                                                            </button>

                                                            <button class="btn dropdown-toggle dropdown-toggle-split ms-1"
                                                                type="button"
                                                                id="conversationDropdown"
                                                                data-bs-toggle="dropdown"
                                                                aria-expanded="false">
                                                            </button>

                                                            <ul class="dropdown-menu" aria-labelledby="conversationDropdown">
                                                                <li><a class="dropdown-item filter-option active" href="#" data-filter="All" data-image="{{ asset('website') }}/images/chat_all_icon.svg"><span><img src="{{ asset('website') }}/images/chat_all_icon.svg" alt=""></span><span>All</span></a></li>
                                                                <li><a class="dropdown-item filter-option" href="#" data-filter="Provider" data-image="{{ asset('website') }}/images/chat_provider_icon.svg"><span><img src="{{ asset('website') }}/images/chat_provider_icon.svg" alt=""></span><span>Provider</span></a></li>
                                                                <li><a class="dropdown-item filter-option" href="#" data-filter="Traveller" data-image="{{ asset('website') }}/images/chat_traveller_icon.svg"><span><img src="{{ asset('website') }}/images/chat_traveller_icon.svg" alt=""></span><span>Traveller</span></a></li>
                                                                <li><a class="dropdown-item filter-option" href="#" data-filter="Unread" data-image="{{ asset('website') }}/images/chat_unread_icon.svg"><span><img src="{{ asset('website') }}/images/chat_unread_icon.svg" alt=""></span><span>Unread</span></a></li>
                                                            </ul>
                                                        </li>

                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link" id="pills-support-tab"
                                                                data-bs-toggle="pill" data-bs-target="#pills-support"
                                                                type="button" role="tab" aria-controls="pills-support"
                                                                aria-selected="false">LuxuStars Support</button>
                                                        </li>
                                                        
                                                    </ul>
                                                    <div class="tab-content" id="pills-tabContent">
                                                        <div class="tab-pane fade show active" id="pills-conversations"
                                                            role="tabpanel" aria-labelledby="pills-conversations-tab">
                                                            <div class="inner_section_chat_threads">

                                                                <div class="chat_threads_search">
                                                                    <i class="fas fa-search"></i>
                                                                    <input type="search" id="search-user-input"
                                                                        class="form-control" aria-describedby="emailHelp"
                                                                        placeholder="Search">
                                                                </div>

                                                                {{-- Chat Threads Container --}}
                                                                <div class="chat_threads_wrapper scrollable-section"
                                                                    id="chat-threads-container">

                                                                </div>
                                                                {{-- Chat Threads Container End --}}

                                                            </div>
                                                        </div>
                                                        <div class="tab-pane fade" id="pills-support" role="tabpanel"
                                                            aria-labelledby="pills-support-tab">
                                                            <div class="inner_section_chat_threads">

                                                                <div class="chat_threads_search">
                                                                    <a href="javascript:void(0)"
                                                                        class="btn start_new_convo_btn">Start new support
                                                                        chat</a>
                                                                </div>

                                                                <div class="chat_threads_wrapper scrollable-section"
                                                                    id="support-chat-threads">

                                                                    @for ($i = 0; $i < 10; $i++)
                                                                        <div class="single_chat_thread">
                                                                            <div class="user_profile_picture">
                                                                                <img src="{{ asset('website') }}/images/user1.png"
                                                                                    alt="User Profile">
                                                                            </div>
                                                                            <div class="chat_details_wrapper">
                                                                                <div class="username_date_wrapper">
                                                                                    <div class="user_name">
                                                                                        <h6>User Name</h6>
                                                                                    </div>
                                                                                    <div class="chat_date">
                                                                                        <span>12:35 PM</span>
                                                                                    </div>
                                                                                </div>
                                                                                {{-- <div class="open_chat_icon">
                                                                                    <i class="fas fa-chevron-right"></i>
                                                                                </div> --}}
                                                                                <div class="chat_preview">
                                                                                    <p>Thank you very much, I am waiting for
                                                                                        the parcel.</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endfor

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-9" id="message-container">
                                            <div
                                                style="display: flex; justify-content: center; align-items: center; height: 100%;">
                                                Select Conversation
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection


@push('js')
    <script src="https://js.pusher.com/8.4.0/pusher.min.js"></script>

    {{-- Audio element for notification sound --}}
    <audio id="notification-audio" preload="auto">
        <source src="{{ asset('website/messenger/notification-audio.wav') }}" type="audio/wav">
    </audio>

    {{-- Pass configuration to JavaScript --}}
    <script>
        // Set messenger configuration in window object for script.js to access
        window.messengerConfig = {
            auth_id: "{{ auth()->user()->ids }}",
            @if($conversation_ids)
            active_conversation_id: "{{ $conversation_ids }}",
            @endif
            routes: {
                getConversations: "{{ route('message.get_conversations') }}",
                fetchConversation: "{{ route('message.fetch_conversation', 'private') }}",
                parseMessage: "{{ route('message.parse_message', 'CONVERSATION_ID') }}",
                loadMoreMessages: "{{ route('message.load_more_messages', 'CONVERSATION_ID') }}",
                messageIndex: "{{ route('message.index', 'CONVERSATION_ID') }}"
            }
        };
    </script>

    {{-- Load the main messenger script --}}
    <script src="{{ asset('website/messenger/script.js') }}"></script>
@endpush
